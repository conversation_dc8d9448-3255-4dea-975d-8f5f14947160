import { Context } from 'https://deno.land/x/oak@v12.6.1/mod.ts';
import htmlTaskService from '../services/htmlTaskService.ts';
import { ApiResponse, CreateHtmlTaskRequest, TaskStatus } from '../types/index.ts';

interface ContextWithParams extends Context {
  params: Record<string, string>;
}

export class HtmlTaskController {
  // 创建HTML生成任务
  async createTask(ctx: Context) {
    try {
      const body = await ctx.request.body().value;
      
      if (!body.prompt) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: 'prompt参数是必需的'
        };
        return;
      }

      const taskData: CreateHtmlTaskRequest = {
        prompt: body.prompt,
        imageUrl: body.imageUrl,
        model: body.model
      };

      const taskId = await htmlTaskService.createTask(taskData);

      ctx.response.body = {
        success: true,
        data: {
          taskId,
          message: '任务已创建，正在处理中...'
        }
      };
    } catch (error) {
      console.error('创建HTML生成任务失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '创建任务失败'
      };
    }
  }

  // 获取任务详情
  async getTask(ctx: ContextWithParams) {
    try {
      const taskId = ctx.params.id;
      
      if (!taskId) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '任务ID不能为空'
        };
        return;
      }

      const task = await htmlTaskService.getTaskById(taskId);
      
      if (!task) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '任务不存在'
        };
        return;
      }

      ctx.response.body = {
        success: true,
        data: task
      };
    } catch (error) {
      console.error('获取任务详情失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取任务详情失败'
      };
    }
  }

  // 获取任务列表
  async getTasks(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      const status = url.searchParams.get('status') as TaskStatus | undefined;

      if (page < 1 || limit < 1 || limit > 100) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '页码必须大于0，每页数量必须在1-100之间'
        };
        return;
      }

      const result = await htmlTaskService.getTasks(page, limit, status);

      ctx.response.body = {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取任务列表失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取任务列表失败'
      };
    }
  }

  // 取消任务
  async cancelTask(ctx: ContextWithParams) {
    try {
      const taskId = ctx.params.id;
      
      if (!taskId) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '任务ID不能为空'
        };
        return;
      }

      const success = await htmlTaskService.cancelTask(taskId);
      
      if (!success) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '任务不存在或无法取消（任务可能已完成或已取消）'
        };
        return;
      }

      ctx.response.body = {
        success: true,
        data: {
          message: '任务已成功取消'
        }
      };
    } catch (error) {
      console.error('取消任务失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '取消任务失败'
      };
    }
  }

  // 获取队列状态
  async getQueueStatus(ctx: Context) {
    try {
      const status = htmlTaskService.getQueueStatus();

      ctx.response.body = {
        success: true,
        data: status
      };
    } catch (error) {
      console.error('获取队列状态失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取队列状态失败'
      };
    }
  }
}

export default new HtmlTaskController(); 